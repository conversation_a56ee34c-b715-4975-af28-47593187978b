{% extends 'base.html' %}
{% load discount_tags i18n %}

{% block title %}{{ venue.venue_name }} - {{ venue.service_provider.business_name }}{% endblock %}

{% block extra_css %}
<style>
    /* CozyWish Design System - Venue Detail */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Breadcrumb Styling */
    .breadcrumb-cw {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        box-shadow: var(--cw-shadow-sm);
    }

    .breadcrumb-cw .breadcrumb-item a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .breadcrumb-cw .breadcrumb-item a:hover {
        color: var(--cw-brand-light);
    }

    .breadcrumb-cw .breadcrumb-item.active {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    /* Card Styling */
    .card-cw {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .card-cw-featured {
        border: 2px solid var(--cw-brand-primary);
        background: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Badge Styling */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
    }

    .badge-cw-secondary {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    /* Accordion Styling */
    .accordion-cw .accordion-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .accordion-cw .accordion-button {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        font-weight: 600;
        border: none;
        padding: 1.25rem 1.5rem;
    }

    .accordion-cw .accordion-button:not(.collapsed) {
        background: var(--cw-brand-accent);
        color: var(--cw-brand-primary);
        box-shadow: none;
    }

    .accordion-cw .accordion-body {
        background: white;
        padding: 1.5rem;
    }

    /* Service Item Styling */
    .service-item {
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        transition: all 0.2s ease;
    }

    .service-item:hover {
        border-color: var(--cw-brand-primary);
        box-shadow: var(--cw-shadow-md);
    }

    .service-name {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .service-description {
        color: var(--cw-neutral-600);
        margin-bottom: 1rem;
    }

    .price-info .text-success {
        color: var(--cw-brand-primary) !important;
    }

    /* Fixed Position Buttons */
    .fixed-action-btn {
        position: fixed;
        bottom: 2rem;
        z-index: 1000;
        border-radius: 50px;
        padding: 1rem 1.5rem;
        box-shadow: var(--cw-shadow-lg);
        border: 2px solid var(--cw-brand-primary);
        background: white;
        color: var(--cw-brand-primary);
        transition: all 0.2s ease;
    }

    .fixed-action-btn:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
    }

    .fixed-action-btn.favorite-active {
        background: var(--cw-brand-primary);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Venue Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-cw">
                    <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_search' %}">Venues</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ venue.venue_name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Venue Information -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <!-- Venue Images Carousel -->
            <div id="venueCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                <div class="carousel-inner rounded overflow-hidden ratio ratio-21x9">
                    {% if primary_image %}
                    <div class="carousel-item active">
                        <img src="{{ primary_image.image.url }}" alt="{{ primary_image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% elif venue.main_image %}
                    <div class="carousel-item active">
                        <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% endif %}

                    {% for image in gallery_images %}
                    <div class="carousel-item {% if not primary_image and not venue.main_image and forloop.first %}active{% endif %}">
                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% empty %}
                    {% if not primary_image and not venue.main_image %}
                    <div class="carousel-item active">
                        <div class="bg-light d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-image fa-4x text-muted"></i>
                            <p class="text-muted mt-3">No images available</p>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>

                <!-- Carousel controls (only show if there are multiple images) -->
                {% if images.count > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>

                <!-- Carousel indicators -->
                <div class="carousel-indicators">
                    {% if primary_image or venue.main_image %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Primary image"></button>
                    {% endif %}
                    {% for image in gallery_images %}
                    <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="{% if primary_image or venue.main_image %}{{ forloop.counter }}{% else %}{{ forloop.counter0 }}{% endif %}" aria-label="Image {{ forloop.counter }}"></button>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Venue Description -->
            <div class="card-cw card-cw-featured mb-4">
                <div class="card-body p-4">
                    <h1 class="card-title h2 mb-3">{{ venue.venue_name }}</h1>
                    <p class="text-muted mb-3">{{ venue.service_provider.business_name }}</p>

                    <!-- Categories -->
                    {% if venue.categories.all %}
                    <div class="mb-3">
                        {% for category in venue.categories.all %}
                            <span class="badge-cw-secondary me-2 mb-2">{{ category.category_name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Price Range -->
                    {% if price_range %}
                    <div class="mb-3">
                        <span class="badge-cw-primary">{{ price_range }}</span>
                    </div>
                    {% endif %}

                    <p class="card-text">{{ venue.short_description }}</p>

                    <!-- Tags -->
                    {% if venue.tags %}
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-tags me-1"></i>
                            {{ venue.tags }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Discount Summary -->
            {% venue_discount_summary venue %}

            <div class="accordion accordion-cw mb-4" id="venueInfoAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingInfo">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInfo" aria-expanded="true" aria-controls="collapseInfo">
                            <i class="fas fa-info-circle me-2"></i>Venue Information
                        </button>
                    </h2>
                    <div id="collapseInfo" class="accordion-collapse collapse show" aria-labelledby="headingInfo">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <h6><i class="fas fa-map-marker-alt me-2"></i>Location</h6>
                                <p class="mb-1">{{ venue.full_address }}</p>
                            </div>
                            <!-- Structured Operating Hours -->
                            {% if venue.operating_hours_set.all %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <div class="row">
                                    {% for hours in venue.operating_hours_set.all %}
                                    <div class="col-md-6 mb-1">
                                        <span class="fw-bold">{{ hours.get_day_display }}:</span>
                                        {% if hours.is_closed %}
                                            <span class="text-muted">Closed</span>
                                        {% else %}
                                            <span>{{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}</span>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            {% elif venue.operating_hours %}
                            <div class="mb-3">
                                <h6><i class="fas fa-clock me-2"></i>Operating Hours</h6>
                                <p class="mb-1">{{ venue.operating_hours|linebreaks }}</p>
                            </div>
                            {% endif %}
                            {% if venue.opening_notes %}
                            <div class="mb-3">
                                <h6><i class="fas fa-exclamation-circle me-2"></i>Special Notes</h6>
                                <p class="mb-1">{{ venue.opening_notes|linebreaks }}</p>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <h6><i class="fas fa-phone me-2"></i>Contact</h6>
                                {% if venue.phone %}
                                    <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ venue.phone }}</p>
                                {% endif %}
                                {% if venue.email %}
                                    <p class="mb-1"><i class="fas fa-envelope me-2"></i>{{ venue.email }}</p>
                                {% endif %}
                                {% if venue.website_url %}
                                    <p class="mb-1"><i class="fas fa-globe me-2"></i><a href="{{ venue.website_url }}" target="_blank" rel="noopener" class="text-decoration-none" style="color: var(--cw-brand-primary);">Visit Website</a></p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amenities Section -->
            {% if venue.amenities.all %}
            <div class="card-cw mb-4">
                <div class="card-body p-4">
                    <h5 class="card-title"><i class="fas fa-star me-2"></i>Amenities & Features</h5>
                    <div class="row">
                        {% for amenity in venue.amenities.all %}
                        {% if amenity.is_active %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2" style="color: var(--cw-brand-primary);"></i>
                                <span>
                                    {% if amenity.custom_name %}
                                        {{ amenity.custom_name }}
                                    {% else %}
                                        {{ amenity.get_amenity_type_display }}
                                    {% endif %}
                                </span>
                            </div>
                            {% if amenity.description %}
                            <small class="text-muted ms-4">{{ amenity.description }}</small>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}


        </div>
    </div>

    <!-- Services Section -->
    {% if services %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="accordion accordion-cw" id="servicesAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingServices">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseServices" aria-expanded="true" aria-controls="collapseServices">
                            <i class="fas fa-spa me-2"></i>Services Offered
                        </button>
                    </h2>
                    <div id="collapseServices" class="accordion-collapse collapse show" aria-labelledby="headingServices">
                        <div class="accordion-body">
                            <!-- Services List - Vertical Layout -->
                            <div class="services-list">
                                {% for service in services %}
                                <div class="service-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <!-- Service Info -->
                                        <div class="service-info flex-grow-1 me-3">
                                            <h5 class="service-name mb-1">{{ service.service_title }}</h5>
                                            <p class="service-description mb-2 small">{{ service.short_description }}</p>

                                            <!-- Price and Duration -->
                                            <div class="service-meta d-flex align-items-center">
                                                {% if service|has_service_discount %}
                                                    <div class="price-info me-3">
                                                        <span class="text-decoration-line-through text-muted me-2">${{ service.price }}</span>
                                                        <span class="text-success fw-bold">${{ service|get_service_discounted_price }}</span>
                                                        <span class="badge-cw-primary ms-2">
                                                            <i class="fas fa-tag me-1"></i>{{ service|get_service_best_discount|format_discount_value }}
                                                        </span>
                                                    </div>
                                                {% else %}
                                                    <span class="fw-bold me-3" style="color: var(--cw-brand-primary);">${{ service.price }}</span>
                                                {% endif %}
                                                <span class="text-muted">{{ service.duration_display }}</span>
                                            </div>
                                        </div>

                                        <!-- Book Service Button -->
                                        <div class="service-action">
                                            <a href="{% url 'venues_app:service_detail' venue_slug=venue.slug service_slug=service.slug %}"
                                               class="btn-cw-primary">
                                                <i class="fas fa-shopping-cart me-1"></i>Book Service
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- FAQs Section -->
    {% if faqs %}
    <div class="row mb-5">
        <div class="col-12">
            <div class="accordion accordion-cw" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFaq">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFaq" aria-expanded="true" aria-controls="collapseFaq">
                            <i class="fas fa-question-circle me-2"></i>Frequently Asked Questions
                        </button>
                    </h2>
                    <div id="collapseFaq" class="accordion-collapse collapse show" aria-labelledby="headingFaq">
                        <div class="accordion-body">
                            {% for faq in faqs %}
                            <div class="faq-item mb-3">
                                <h6 class="fw-bold mb-2" style="color: var(--cw-brand-primary);">{{ faq.question }}</h6>
                                <p class="mb-0">{{ faq.answer|linebreaks }}</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Flag Venue Button (Fixed Position) -->
{% if can_flag %}
<a href="{% url 'venues_app:flag_venue' venue_slug=venue.slug %}" class="fixed-action-btn" style="right: 2rem;">
    <i class="fas fa-flag me-2"></i>{% trans "Report Issue" %}
</a>
{% endif %}

<!-- Favorite Button (Fixed Position) -->
{% if user.is_authenticated and user.is_customer %}
<button id="favorite-btn" data-venue-id="{{ venue.id }}" class="fixed-action-btn" style="left: 2rem;" aria-label="Toggle favorite">
    <i class="fas fa-heart me-2"></i>Favorite
</button>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const btn = document.getElementById('favorite-btn');
  if (!btn) return;
  const venueId = btn.dataset.venueId;
  const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
  function updateStatus(isFav) {
    if (isFav) {
      btn.classList.add('text-danger', 'active');
    } else {
      btn.classList.remove('text-danger', 'active');
    }
  }
  fetch('{% url "dashboard_app:check_favorite_status" venue_id=0 %}'.replace('0', venueId))
    .then(r => r.json())
    .then(data => updateStatus(data.is_favorite));
  btn.addEventListener('click', function(e) {
    e.preventDefault();
    const isFav = btn.classList.contains('active');
    const url = isFav ? '{% url "dashboard_app:remove_favorite_venue" venue_id=0 %}' : '{% url "dashboard_app:add_favorite_venue" venue_id=0 %}';
    fetch(url.replace('0', venueId), {
      method: 'POST',
      headers: { 'X-CSRFToken': csrftoken }
    }).then(r => r.json()).then(data => {
      if (data.success) {
        updateStatus(!isFav);
      }
    });
  });
});
</script>
{% endblock %}
